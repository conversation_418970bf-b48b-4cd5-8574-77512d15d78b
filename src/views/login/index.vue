<template>
  <div class="login_home">
    <div class="login_box" v-show="isLogin">
      <div class="register" @click="toRegister">账号注册</div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="密码登录" name="password">
          <div class="form_content">
            <el-form :model="passwordForm" :rules="rules1" ref="passwordForm">
              <el-form-item prop="name">
                <el-input
                  id="el_name"
                  placeholder="花名或手机号"
                  v-model="passwordForm.name"
                  prefix-icon="el-icon-user-solid"
                ></el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  id="el_password"
                  auto-complete="new-password"
                  placeholder="密码"
                  type="password"
                  v-model="passwordForm.password"
                  prefix-icon="el-icon-s-goods"
                ></el-input>
              </el-form-item>
              <div class="forget_password">
                <div>
                  <el-checkbox
                    name="type"
                    v-model="passwordForm.type"
                  ></el-checkbox>
                  <span class="mgl-5">下次自动登录</span>
                </div>
                <span>忘记密码?</span>
              </div>
              <el-form-item prop="password" v-if="isChecked">
                <el-button type="primary" @click="submitForm('passwordForm')"
                  >登录</el-button
                >
              </el-form-item>
              <el-form-item v-else>
                <el-button type="info" disabled>登录</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="短信登录" name="message">
          <div class="form_content message_form">
            <el-form :model="messageForm" :rules="rules2" ref="messageForm">
              <el-form-item prop="phone">
                <el-input
                  id="el_phone"
                  placeholder="+86"
                  v-model="messageForm.phone"
                  prefix-icon="el-icon-phone"
                ></el-input>
              </el-form-item>
              <el-form-item prop="validCode">
                <el-input
                  auto-complete="new-password"
                  id="el_validCode"
                  placeholder="请输入验证码"
                  type="password"
                  v-model="messageForm.validCode"
                  prefix-icon="el-icon-s-goods"
                ></el-input>
              </el-form-item>
              <div class="forget_password">
                <div>
                  <el-checkbox
                    name="type"
                    v-model="messageForm.type"
                  ></el-checkbox>
                  <span class="mgl-5">下次自动登录</span>
                </div>
                <span>忘记密码?</span>
              </div>
              <el-form-item v-if="isCheckedMassage">
                <el-button type="primary" @click="submitMesForm('messageForm')"
                  >登录</el-button
                >
              </el-form-item>
              <el-form-item v-else>
                <el-button type="info" disabled>登录</el-button>
              </el-form-item>
              <div class="valid_code">
                <el-form-item v-if="isValid">
                  <el-button type="info" disabled
                    >{{ limitTime }}s重新发送</el-button
                  >
                </el-form-item>
                <el-form-item v-else>
                  <el-button type="primary" plain @click="getValidCode"
                    >发送验证码</el-button
                  >
                </el-form-item>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 注册 -->
    <div class="register_box" v-show="!isLogin">
      <div class="login" @click="toLogin">登录</div>
      <div class="title">注册乐知</div>
      <div class="form_content">
        <el-form :model="registerForm" :rules="rules3" ref="registerForm">
          <el-form-item prop="name">
            <el-input
              id="re_name"
              placeholder="花名或手机号"
              v-model="registerForm.name"
              prefix-icon="el-icon-user-solid"
            ></el-input>
          </el-form-item>
          <el-form-item prop="phone">
            <el-input
              id="re_phone"
              placeholder="+86"
              v-model="registerForm.phone"
              prefix-icon="el-icon-phone"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password" class="mgb-80">
            <el-input
              id="re_password"
              auto-complete="new-password"
              placeholder="密码"
              type="password"
              v-model="registerForm.password"
              prefix-icon="el-icon-s-goods"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password" v-if="isCheckedRes">
            <el-button type="primary" @click="submitRegForm('registerForm')"
              >注册</el-button
            >
          </el-form-item>
          <el-form-item v-else>
            <el-button type="info" disabled>注册</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Login",
  data() {
    return {
      activeName: "password",
      passwordForm: {},
      messageForm: {},
      registerForm: {},
      rules1: {
        name: [
          { required: true, message: "请输入花名或手机号", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      rules2: {
        phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
        validCode: [
          { required: true, message: "请输入验证码", trigger: "blur" },
        ],
      },
      rules3: {
        name: [
          { required: true, message: "请输入花名或手机号", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
      },
      isChecked: false,
      isCheckedMassage: false,
      isCheckedRes: false,
      limitTime: 60,
      timer: 0,
      isValid: false,
      isLogin: false,
    };
  },
  methods: {
    getValidCode() {
      if (!this.checkPhone()) {
        return;
      } else {
        this.isValid = true;
        this.limitTimer();
      }
    },
    limitTimer() {
      this.timer = setInterval(() => {
        this.limitTime--;
        if (this.limitTime === 0) {
          clearInterval(this.timer);
          this.isValid = false;
          this.limitTime = 60;
        }
      }, 1000);
    },
    handleClick() {},
    checkPswform(event) {
      this.$refs["passwordForm"].validate((valid) => {
        console.log(valid);
        if (valid) {
          this.isChecked = true;
        } else {
          this.isChecked = false;
          return false;
        }
      });
    },
    checkMesform(event) {
      this.$refs["messageForm"].validate((valid) => {
        console.log(valid);
        if (valid) {
          this.isCheckedMassage = true;
        } else {
          this.isCheckedMassage = false;
          return false;
        }
      });
    },
    checkResform() {
      this.$refs["registerForm"].validate((valid) => {
        console.log(valid);
        if (valid) {
          this.isCheckedRes = true;
        } else {
          this.isCheckedRes = false;
          console.log(valid);
          return false;
        }
      });
    },
    checkPhone() {
      var phone = document.getElementById("el_phone").value;
      if (!/^1[3|4|5|7|8]\d{9}$/.test(phone) || phone == "") {
        console.log("手机号有误");
        return false;
      } else {
        return true;
      }
    },
    submitForm() {},
    submitMesForm() {},
    submitRegForm() {},
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    toRegister() {
      // this.resetForm("passwordForm");
      // this.resetForm("messageForm");
      this.isLogin = false;
    },
    toLogin() {
      // this.resetForm("registerForm");
      this.isLogin = true;
    },
  },
  computed: {},
  mounted() {
    let name = document.getElementById("el_name");
    let psw = document.getElementById("el_password");
    let phone = document.getElementById("el_phone");
    let validCode = document.getElementById("el_validCode");

    let rename = document.getElementById("re_name");
    let repsw = document.getElementById("re_password");
    let rephone = document.getElementById("re_phone");

    rename.addEventListener("blur", this.checkResform, false);
    repsw.addEventListener("blur", this.checkResform, false);
    rephone.addEventListener("blur", this.checkResform, false);

    phone.addEventListener("blur", this.checkMesform, false);
    validCode.addEventListener("blur", this.checkMesform, false);
    name.addEventListener("blur", this.checkPswform, false);
    psw.addEventListener("blur", this.checkPswform, false);
  },
  destroyed() {
    clearInterval(this.timer);
  },
};
</script>
<style>
</style>
