<template>
  <div>
    <NavComp/>
    <el-card class="pdt-60 box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="14">
          <el-col :span="14" :offset="5">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="flex: 1; text-align: center;">
                <h2>专题创建角色管理</h2>
              </div>
              <div>
                <el-button type="primary" plain @click="handleOpenAddDialog">新增</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="14">
        <el-col :span="14" :offset="5">
          <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="150">
            </el-table-column>
            <el-table-column
              prop="name"
              label="名称"
              width="300">
            </el-table-column>
            <el-table-column
              prop="cteTime"
              label="创建时间"
              width="200">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作">
              <template slot-scope="scope">
                <el-button @click="handleGetRoleDetail(scope.row.id)" type="text" size="small">查看</el-button>
                <el-button @click='handleOpenEditDialog(scope.row)' type="text" size="small">编辑</el-button>
                <el-button @click='handleOpenAssignDialog(scope.row)' type="text" size="small">人员分配</el-button>
                <el-button @click='deleteRole(scope.row)' type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog title="新增角色" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="名称" :label-width="formLabelWidth">
            <el-input v-model="form.name" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAddRoleSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="编辑角色" :visible.sync="editDialogFormVisible">
        <el-form :model="form">
          <el-form-item label="名称" :label-width="formLabelWidth">
            <el-input v-model="form.name" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleUpdateRoleSubmit">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 详情弹窗 -->
      <el-dialog title="角色详情" :visible.sync="detailDialogVisible" width="70%" top="5vh">
        <div class="detail-content">
          <div class="detail-section">
            <div class="detail-item">
              <span class="label">角色ID：</span>
              <span class="value">{{ roleDetail.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">角色名称：</span>
              <span class="value role-name">{{ roleDetail.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ roleDetail.cteTime }}</span>
            </div>
            <div class="detail-item">
              <span class="label">已分配人员：</span>
              <div class="value">
                <div v-if="roleDetail.assignedUsers && roleDetail.assignedUsers.length > 0" class="assigned-users">
                  <el-tag
                    v-for="user in roleDetail.assignedUsers"
                    :key="user.id"
                    type="success"
                    class="user-tag">
                    {{ user.name }}({{ user.account }}) - {{ user.department }}
                  </el-tag>
                </div>
                <span v-else class="no-users">暂无分配人员</span>
              </div>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 人员分配对话框 -->
      <el-dialog :title="`${currentRoleName} - 人员分配`" :visible.sync="assignDialogVisible" width="80%" top="5vh">
        <div class="assign-content">
          <div class="assign-header">
            <h4>为角色"{{ currentRoleName }}"分配人员</h4>
            <p>左侧为可分配人员，右侧为已分配人员。可通过拖拽或点击按钮进行人员分配。</p>
          </div>
          <el-transfer
            v-model="assignedUserIds"
            :data="transferData"
            :titles="['可分配人员', '已分配人员']"
            :button-texts="['移除', '分配']"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}'
            }"
            filterable
            :filter-placeholder="'请输入姓名或账号'"
            :filter-method="filterMethod"
            style="text-align: left; display: inline-block;"
            :props="{
              key: 'key',
              label: 'label',
              disabled: 'disabled'
            }">
            <template slot-scope="{ option }">
              <span>{{ option.label }} ({{ option.account }}) - {{ option.department }}</span>
            </template>
          </el-transfer>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSaveAssignment">保 存</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from '@/api/httpNew';
import {roles, users, roleUserAssignments, getRandomNumber} from './data';
import NavComp from './navComp.vue';

const api = new httpNew();
export default {
  name: 'role',
  components: {NavComp},
  data() {
    return {
      tableData: [],
      localRoles: JSON.parse(JSON.stringify(roles)), // 深拷贝角色数据
      localUsers: JSON.parse(JSON.stringify(users)), // 深拷贝用户数据
      localRoleUserAssignments: JSON.parse(JSON.stringify(roleUserAssignments)), // 深拷贝角色人员关联数据
      roleDetail: {},
      dialogFormVisible: false,
      editDialogFormVisible: false,
      detailDialogVisible: false,
      assignDialogVisible: false,
      form: {
        id: '',
        name: '', // 修改为name字段保持一致
      },
      formLabelWidth: '120px',
      // 人员分配相关数据
      currentRoleId: null,
      currentRoleName: '',
      transferData: [], // 穿梭框数据源
      assignedUserIds: [], // 已分配的用户ID列表
    }
  },
  created() {
    this.handleGetRoleList();
  },
  methods: {
    // 获取当前时间的格式化字符串
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 获取角色列表
    handleGetRoleList() {
      // 保留API调用但不依赖其结果
      api.getRoleList && api.getRoleList().then(res => {
        console.log('角色列表API调用成功', res);
      }).catch(err => {
        console.log('角色列表API调用失败', err);
      });

      // 使用本地数据
      this.tableData = [...this.localRoles];
    },

    handleOpenAddDialog(){
      // 重置表单
      this.form = {
        id: '',
        name: '',
      };
      this.dialogFormVisible = true;
    },

    handleOpenEditDialog(row){
      // 使用深拷贝避免数据引用问题
      this.form = {
        id: row.id,
        name: row.name,
        cteTime: row.cteTime
      };
      this.editDialogFormVisible = true;
    },
    // 新增角色提交方法
    handleAddRoleSubmit() {
      // 表单验证
      if (!this.form.name || this.form.name.trim() === '') {
        this.$message.error("请输入角色名称");
        return;
      }

      // 检查角色名称是否重复
      const exists = this.localRoles.find(item => item.name === this.form.name.trim());
      if (exists) {
        this.$message.error("角色名称已存在");
        return;
      }

      this.addSubjectRole();
      this.dialogFormVisible = false; // 关闭弹窗
    },

    addSubjectRole(){
      // 生成新的角色对象
      const newRole = {
        id: getRandomNumber(100, 999),
        name: this.form.name.trim(),
        cteTime: this.getCurrentTime()
      };

      // 先执行本地操作
      this.localRoles.push(newRole);
      this.handleGetRoleList(); // 刷新表格数据
      this.$message({message: '新增角色成功', type:'success'});

      // 保留API调用但不依赖其结果
      api.addRole(this.form).then(res => {
        console.log('新增角色API调用成功', res);
      }).catch(err => {
        console.log('新增角色API调用失败', err);
      });
    },
    // 编辑角色提交方法
    handleUpdateRoleSubmit() {
      // 表单验证
      if (!this.form.name || this.form.name.trim() === '') {
        this.$message.error("请输入角色名称");
        return;
      }

      // 检查角色名称是否重复（排除自己）
      const exists = this.localRoles.find(item => item.name === this.form.name.trim() && item.id !== this.form.id);
      if (exists) {
        this.$message.error("角色名称已存在");
        return;
      }

      this.updateRole();
      this.editDialogFormVisible = false; // 关闭弹窗
    },

    updateRole() {
      // 先执行本地操作
      const index = this.localRoles.findIndex(item => item.id === this.form.id);
      if (index !== -1) {
        // 更新本地数据，保留原有的id和cteTime
        this.localRoles[index] = {
          ...this.localRoles[index],
          name: this.form.name.trim()
        };
        this.handleGetRoleList(); // 刷新表格数据
        this.$message({message: '编辑角色成功', type:'success'});
      } else {
        this.$message({message: '未找到要编辑的角色', type: 'error'});
        return;
      }

      // 保留API调用但不依赖其结果
      api.editRole(this.form).then(res => {
        console.log('编辑角色API调用成功', res);
      }).catch(err => {
        console.log('编辑角色API调用失败', err);
      });
    },
    deleteRole(row) {
      // 添加二次确认弹窗
      this.$confirm('此操作将永久删除该角色, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除，执行本地操作
        const index = this.localRoles.findIndex(item => item.id === row.id);
        if (index !== -1) {
          this.localRoles.splice(index, 1); // 从数组中删除
          this.handleGetRoleList(); // 刷新表格数据
          this.$message({message: '删除角色成功', type:'success'});

          // 保留API调用但不依赖其结果
          api.removeRole(row.id).then(res => {
            console.log('删除角色API调用成功', res);
          }).catch(err => {
            console.log('删除角色API调用失败', err);
          });
        } else {
          this.$message({message: '未找到要删除的角色', type: 'error'});
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 查看角色详情
    handleGetRoleDetail(id) {
      // 从本地数据中查找
      const role = this.localRoles.find(item => item.id === id);
      if (role) {
        // 获取该角色已分配的人员信息
        const assignment = this.localRoleUserAssignments.find(item => item.roleId === id);
        const assignedUsers = assignment ?
          this.localUsers.filter(user => assignment.userIds.includes(user.id)) : [];

        this.roleDetail = {
          ...role,
          assignedUsers: assignedUsers
        };
        this.detailDialogVisible = true; // 打开详情弹窗
      } else {
        this.$message.error("详情查询失败");
        return;
      }

      // 保留API调用但不依赖其结果
      api.getRoleDetail && api.getRoleDetail(id).then(res => {
        console.log('详情API调用成功', res);
      }).catch(err => {
        console.log('详情API调用失败', err);
      });
    },

    // 打开人员分配对话框
    handleOpenAssignDialog(row) {
      this.currentRoleId = row.id;
      this.currentRoleName = row.name;

      // 准备穿梭框数据源
      this.transferData = this.localUsers.map(user => ({
        key: user.id,
        label: user.name,
        account: user.account,
        department: user.department,
        disabled: false
      }));

      // 获取当前角色已分配的用户ID
      const assignment = this.localRoleUserAssignments.find(item => item.roleId === row.id);
      this.assignedUserIds = assignment ? [...assignment.userIds] : [];

      this.assignDialogVisible = true;
    },

    // 穿梭框过滤方法
    filterMethod(query, item) {
      return item.label.indexOf(query) > -1 || item.account.indexOf(query) > -1;
    },

    // 保存人员分配
    handleSaveAssignment() {
      // 查找或创建角色人员关联记录
      const assignmentIndex = this.localRoleUserAssignments.findIndex(item => item.roleId === this.currentRoleId);

      if (assignmentIndex !== -1) {
        // 更新现有关联
        this.localRoleUserAssignments[assignmentIndex].userIds = [...this.assignedUserIds];
      } else {
        // 创建新的关联
        this.localRoleUserAssignments.push({
          roleId: this.currentRoleId,
          userIds: [...this.assignedUserIds]
        });
      }

      this.$message.success(`角色"${this.currentRoleName}"的人员分配已保存`);
      this.assignDialogVisible = false;

      // 保留API调用但不依赖其结果
      api.saveRoleUserAssignment && api.saveRoleUserAssignment({
        roleId: this.currentRoleId,
        userIds: this.assignedUserIds
      }).then(res => {
        console.log('人员分配API调用成功', res);
      }).catch(err => {
        console.log('人员分配API调用失败', err);
      });
    }
  }
}
</script>

<style scoped>
/* 详情弹窗样式 */
.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-item .label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.role-name {
  background-color: #F0F9FF;
  color: #1890FF;
  padding: 6px 10px;
  border-radius: 4px;
  font-weight: 500;
}

/* 人员分配样式 */
.assign-content {
  padding: 20px 0;
}

.assign-header {
  margin-bottom: 20px;
  text-align: center;
}

.assign-header h4 {
  color: #303133;
  margin-bottom: 8px;
}

.assign-header p {
  color: #909399;
  font-size: 14px;
  margin: 0;
}



.assigned-users {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.user-tag {
  margin: 2px;
  font-size: 12px;
}

.no-users {
  color: #C0C4CC;
  font-style: italic;
}

/* 穿梭框宽度优化 */
.el-transfer-panel {
  width: 300px !important;
}
</style>
