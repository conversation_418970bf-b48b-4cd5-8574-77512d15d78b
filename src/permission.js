import router from './router'
import store from './store'
import httpCore from "./api/httpCore";
import localStore from "./utils/localstorage";
import Cookies from 'js-cookie'
import {Message} from "element-ui";
import { MessageBox } from 'element-ui';
const api = new httpCore();
const whiteList = ['/login','/articleDetail'] // 白名单

const token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.mT-93nTkSO92gDOX0FL2lsggFIheR-lpvCO4lwCs0gs'

router.beforeEach((to, from, next) => {
  /*if(whiteList.indexOf(to.path) !== -1){
    if('/articleDetail'==to.path){
      if(localStorage.getItem('login')!=null){
        // store.commit('setLoginInfo',JSON.parse(localStorage.getItem('login')));
        //用token里的用户信息
        let locallogin = JSON.parse(localStorage.getItem('login'));
        let flag = '';
        flag = Cookies.get("acctoken");
        if('undefined' == flag || null == flag || '' == flag ){
          flag = Cookies.get("token");
        }
        if('undefined' != flag && null != flag && '' != flag){
          let strings = flag.split("."); //截取token，获取载体
          let tokenbody = JSON.parse(decodeURIComponent(escape(window.atob(strings[1].replace(/-/g, "+").replace(/_/g, "/"))))); //解析，需要吧‘_’,'-'进行转换否则会无法解析
          if('undefined' != tokenbody.sub && null != tokenbody.sub && '' != tokenbody.sub){
            let userinfo = JSON.parse(tokenbody.sub);
            locallogin.id = userinfo.id;
            locallogin.name = userinfo.name;
            locallogin.account = userinfo.account;
            locallogin.orgId = userinfo.orgId;
            locallogin.email = userinfo.email;
            locallogin.mobile = userinfo.mobile;
          }
        }
        store.commit('setLoginInfo',locallogin);
      }
    }
    next()
  }else {
    // 判断是否过期
    let validDate = localStore.get("valid_date")
    let port = window.location.port;
    let flag = '';
    if(port === '12000' || port === '10362' || port === '12070' || port === '10406' || port === '1100'){
      flag = Cookies.get("steward");
    }else{
      flag = Cookies.get("token");
    }
    if('undefined' != Cookies.get("acctoken") && null != Cookies.get("acctoken") && '' != Cookies.get("acctoken")){
      flag = Cookies.get("acctoken");
    }
    // let token = Cookies.get("token")
    // let userId = store.state.loginInfo.userId;
    // 判断token是否存在
    if ('undefined' == flag || null == flag || '' == flag || null === validDate) {
      localStore.clear();
      this.$router.push({name: 'tenantLogin'})
    } else if (validDate){
      let currentTime = new Date().getTime()
      if (currentTime < parseInt(validDate)) {
        // store.commit('setLoginInfo',JSON.parse(localStorage.getItem('login')));
        //因为被查了漏洞，改用token里的用户信息
        let locallogin = JSON.parse(localStorage.getItem('login'));
        let strings = flag.split("."); //截取token，获取载体
        let tokenbody = JSON.parse(decodeURIComponent(escape(window.atob(strings[1].replace(/-/g, "+").replace(/_/g, "/"))))); //解析，需要吧‘_’,'-'进行转换否则会无法解析
        if('undefined' != tokenbody.sub && null != tokenbody.sub && '' != tokenbody.sub){
          let userinfo = JSON.parse(tokenbody.sub);
          locallogin.id = userinfo.id;
          locallogin.name = userinfo.name;
          locallogin.account = userinfo.account;
          locallogin.orgId = userinfo.orgId;
          locallogin.email = userinfo.email;
          locallogin.mobile = userinfo.mobile;
        }
        store.commit('setLoginInfo',locallogin);
        // console.log(store.state.loginInfo);
        var isFirstVisit = sessionStorage.getItem('isFirstVisit');//为了弹公告
        if(!isFirstVisit){
        	sessionStorage.setItem("isFirstVisit", "1");
        }
        next()
      } else {
        // Message({
        //   message: "登录失效，请重新登录",
        //   type: 'error',
        //   showClose: true,
        //   duration: 5 * 1000
        // })
        MessageBox.alert('请重新登录', '登录失效', {
                  confirmButtonText: '确定',
                  callback: action => {
                    window.location.href ='/steward/index.html#/tenantLogin';
                  }
                });
        localStore.clear();
        this.$router.push({name: 'tenantLogin'})
      }
    }
  }*/

	//token认证
	/*api.getLogin().then(response => {
		if(response.data.retCode == '0'){
			var isFirstVisit = sessionStorage.getItem('isFirstVisit');
			if(!isFirstVisit){
				sessionStorage.setItem("isFirstVisit", "1");
			}
      store.commit('setLoginInfo',response.data.data.entity);
			next();
		}else {
	    if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
	      next()
	    } else {
           window.location.href ='/npage/home.html'
	      // 上海省份跳转地址
//	      window.location.href ='/itsm/npage/lezhilogin.html' // 否则全部重定向到登录页
	    }
	  }
	}).catch(err => {
		console.log("token认证失败!"+err);
	});*/
  Cookies.set("token", token);
  store.commit('setLoginInfo', {
    id: "0",
    account: "admin",
    name: "管理员",
    mobile: "***********",
    email: "<EMAIL>",
    orgId: "7ed66463f82f4e52882db6a926f83b95",
    sysRoles: [{id: "portal_admin"}, {id: "0559b026c1e5489989e67055cec7ad52"}]
  });
  next()

})
